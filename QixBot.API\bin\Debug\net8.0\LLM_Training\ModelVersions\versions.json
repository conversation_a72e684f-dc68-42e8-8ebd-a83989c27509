[{"Version": "v20250630_231756", "Timestamp": "2025-06-30T23:17:56.8483464Z", "Description": "Pre-incremental-update checkpoint", "Performance": 0.4, "IsActive": false, "IsCheckpoint": true, "ValidationResults": null, "Metadata": {}, "Changes": [], "ModelPath": ""}, {"Version": "v20250702_201514", "Timestamp": "2025-07-02T20:15:14.7674749Z", "Description": "Pre-incremental-update checkpoint", "Performance": 0.6, "IsActive": false, "IsCheckpoint": true, "ValidationResults": null, "Metadata": {}, "Changes": [], "ModelPath": ""}, {"Version": "v20250702_201514_updated", "Timestamp": "2025-07-02T20:15:14.7918005Z", "Description": "Incrementally updated model", "Performance": 0.6, "IsActive": false, "IsCheckpoint": false, "ValidationResults": {"ValidationTime": "2025-07-02T20:15:14.7881688Z", "CurrentPerformance": 0.6, "BaselinePerformance": 0.5, "PerformanceGain": 0.1, "PerformanceImproved": true, "StabilityScore": 0, "RobustnessScore": 0.75, "SafetyScore": 0.85, "PassedValidation": false, "ErrorMessage": "", "ValidationMetrics": {}, "ValidationTests": []}, "Metadata": {}, "Changes": [], "ModelPath": ""}, {"Version": "v20250703_020941", "Timestamp": "2025-07-03T02:09:41.6686041Z", "Description": "Pre-incremental-update checkpoint", "Performance": 0.6, "IsActive": false, "IsCheckpoint": true, "ValidationResults": null, "Metadata": {}, "Changes": [], "ModelPath": ""}, {"Version": "v20250703_020941_updated", "Timestamp": "2025-07-03T02:09:41.6960192Z", "Description": "Incrementally updated model", "Performance": 0.6, "IsActive": false, "IsCheckpoint": false, "ValidationResults": {"ValidationTime": "2025-07-03T02:09:41.6925394Z", "CurrentPerformance": 0.6, "BaselinePerformance": 0.5, "PerformanceGain": 0.1, "PerformanceImproved": true, "StabilityScore": 0, "RobustnessScore": 0.75, "SafetyScore": 0.85, "PassedValidation": false, "ErrorMessage": "", "ValidationMetrics": {}, "ValidationTests": []}, "Metadata": {}, "Changes": [], "ModelPath": ""}, {"Version": "v20250704_153740", "Timestamp": "2025-07-04T15:37:40.5643937Z", "Description": "Pre-incremental-update checkpoint", "Performance": 0.4, "IsActive": true, "IsCheckpoint": true, "ValidationResults": null, "Metadata": {}, "Changes": [], "ModelPath": ""}]